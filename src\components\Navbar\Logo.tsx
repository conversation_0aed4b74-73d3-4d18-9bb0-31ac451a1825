const Logo = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="40"
      height="32"
      viewBox="0 0 40 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <path
        d="M29.2323 29.9192C30.0404 29.9192 30.8283 29.5354 31.3333 28.9091L39.2121 18.8687C40.2626 17.5151 40.2626 15.6364 39.2121 14.2828L31.3333 4.24242C30.8283 3.59596 30.0606 3.23232 29.2323 3.23232H26.5455L25.0101 6.92929H26.9697C27.5556 6.92929 28.1212 7.19192 28.4848 7.65656L34.1818 14.9293C34.9495 15.899 34.9495 17.2727 34.1818 18.2424L28.4848 25.5151C28.1212 25.9798 27.5556 26.2424 26.9697 26.2424H15.697L14.1414 29.9192H29.2323Z"
        fill="url(#paint0_linear_271_2154)"
      />
      <path
        d="M10.7677 0C9.9596 0 9.17172 0.383838 8.66667 1.0101L0.787879 11.0505C-0.262626 12.404 -0.262626 14.2828 0.787879 15.6364L8.66667 25.6768C9.17172 26.3232 9.93939 26.6869 10.7677 26.6869H13.4545L14.9899 22.9899H13.0303C12.4444 22.9899 11.8788 22.7273 11.5152 22.2626L5.81818 14.9899C5.0505 14.0202 5.0505 12.6465 5.81818 11.6768L11.5152 4.40404C11.8788 3.93939 12.4444 3.67677 13.0303 3.67677H24.303L25.8586 0H10.7677Z"
        fill="url(#paint1_linear_271_2154)"
      />
      <path
        d="M17.5758 30H14.1616L25.2323 3.29297H28.6667L17.5758 30Z"
        fill="url(#paint2_linear_271_2154)"
      />
      <path
        d="M14.7877 26.707H11.3533L22.4241 -3.8147e-05H25.8584L14.7877 26.707Z"
        fill="url(#paint3_linear_271_2154)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_271_2154"
          x1="41.0928"
          y1="16.5705"
          x2="21.3798"
          y2="16.5705"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="var(--gradient-start)" />
          <stop offset="0.1482" stopColor="var(--gradient-start)" stopOpacity="0.85" />
          <stop offset="0.4004" stopColor="var(--gradient-start)" stopOpacity="0.6" />
          <stop offset="0.7237" stopColor="var(--gradient-mid)" stopOpacity="0.2763" />
          <stop offset="1" stopColor="var(--gradient-mid)" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_271_2154"
          x1="-1.09277"
          y1="13.3487"
          x2="18.6202"
          y2="13.3487"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="var(--gradient-start)" />
          <stop offset="0.1482" stopColor="var(--gradient-start)" stopOpacity="0.85" />
          <stop offset="0.4004" stopColor="var(--gradient-start)" stopOpacity="0.6" />
          <stop offset="0.7237" stopColor="var(--gradient-mid)" stopOpacity="0.2763" />
          <stop offset="1" stopColor="var(--gradient-mid)" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_271_2154"
          x1="13.5486"
          y1="16.6518"
          x2="24.6064"
          y2="16.6518"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="var(--gradient-start)" />
          <stop offset="0.1482" stopColor="var(--gradient-start)" stopOpacity="0.85" />
          <stop offset="0.4004" stopColor="var(--gradient-start)" stopOpacity="0.6" />
          <stop offset="0.7237" stopColor="var(--gradient-mid)" stopOpacity="0.2763" />
          <stop offset="1" stopColor="var(--gradient-mid)" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_271_2154"
          x1="26.4714"
          y1="13.3482"
          x2="15.4136"
          y2="13.3482"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="var(--gradient-start)" />
          <stop offset="0.1482" stopColor="var(--gradient-start)" stopOpacity="0.85" />
          <stop offset="0.4004" stopColor="var(--gradient-start)" stopOpacity="0.6" />
          <stop offset="0.7237" stopColor="var(--gradient-mid)" stopOpacity="0.2763" />
          <stop offset="1" stopColor="var(--gradient-mid)" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export default Logo
