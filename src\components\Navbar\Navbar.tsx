'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import { BurgerIcon, CloseIcon } from '../../utils/icons'
import logo2 from '@/app/logo.png'
import Image from 'next/image'
import '@/assets/css/movement.css'
import DayNightToggle from '../Theme/DayNightToggle'

const navItems = [
  {
    label: 'About Me',
    href: '/#aboutme',
  },
  {
    label: 'Skills',
    href: '/#skills',
  },
  {
    label: 'Certifications',
    href: '/#certifications',
  },
]

const Navbar = () => {
  const [isVisible, setIsVisible] = useState(false)
  const pathname = usePathname()

  const toggleMenu = () => {
    setIsVisible(!isVisible)
  }

  return (
    <nav className="movement-inner bg-primary relative overflow-hidden rounded-[2rem] sm:rounded-[3rem] lg:rounded-[4rem] mx-2 sm:mx-4 my-4 sm:my-6 lg:my-8">
      {/* iOS-style Liquid Glass Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-violet-500/5 to-purple-600/10"></div>

      {/* Floating glass orbs with iOS-style blur */}
      <div className="absolute top-5 left-1/4 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-5 right-1/4 w-48 h-48 bg-gradient-to-br from-violet-400/15 to-purple-600/25 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-5 w-32 h-32 bg-gradient-to-br from-blue-300/10 to-violet-400/15 rounded-full blur-2xl animate-pulse delay-500"></div>

      {/* Additional ambient lighting */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-blue-500/2 to-violet-500/3"></div>

      <div className="mx-auto flex h-15 w-full items-center px-4 py-1 relative z-10">
        {/* Logo on the left */}
        <div className="animate-fade-up text-primary-content relative flex items-center transition-all duration-300 md:static p-2 rounded-2xl backdrop-blur-2xl hover:bg-white/10 hover:shadow-xl hover:scale-105">
          <Image src={logo2} alt="Logo" width={40} className="drop-shadow-lg"/>
        </div>

        {/* Navigation items centered */}
        <ul
          className={`${
            isVisible ? 'flex' : 'hidden'
          } animate-fade-in absolute top-16 left-0 z-20 h-dvh w-dvw flex-col bg-primary/95 backdrop-blur-3xl border border-white/10 md:static md:top-0 md:flex md:h-full md:w-auto md:flex-row md:mx-auto md:gap-8 lg:gap-12 md:bg-transparent md:backdrop-blur-none md:border-none`}>
          {navItems.map(({ label, href }) => (
            <li
              key={href}
              onClick={() => setIsVisible(false)}
              className="flex items-center px-4 text-2xl md:text-base lg:px-4">
              <Link
                href={href}
                className={`text-primary-content hover:text-neutral w-full py-7 transition-all duration-300 md:py-2 md:px-4 md:rounded-2xl md:backdrop-blur-2xl md:hover:bg-white/10 md:hover:shadow-xl md:hover:border md:hover:border-white/10 md:hover:scale-105 ${
                  pathname === href ? 'text-neutral cursor-text md:bg-gradient-to-br md:from-blue-500/30 md:to-violet-600/30 md:shadow-2xl md:border md:border-white/20 md:scale-105' : ''
                }`}>
                {label}
              </Link>
            </li>
          ))}
        </ul>
        
        {/* Mobile menu button */}
        <div className="md:hidden ml-auto mr-8">
          <button
            onClick={toggleMenu}
            className="p-3 rounded-2xl backdrop-blur-2xl hover:bg-white/10 hover:shadow-xl hover:scale-105 transition-all duration-300 border border-white/10"
          >
            {isVisible ? (
              <CloseIcon className="text-primary-content drop-shadow-lg" />
            ) : (
              <BurgerIcon className="text-primary-content drop-shadow-lg" />
            )}
          </button>
        </div>
        
        {/* Theme Toggle Switch - positioned at far right */}
        <div className="absolute right-0 scale-50 flex items-center">
          <DayNightToggle />
        </div>
      </div>
    </nav>
  )
}

export default Navbar



