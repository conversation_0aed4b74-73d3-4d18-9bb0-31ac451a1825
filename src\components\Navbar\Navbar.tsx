'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import { BurgerIcon, CloseIcon } from '../../utils/icons'
import logo2 from '@/app/logo.png'
import Image from 'next/image'
import '@/assets/css/movement.css'
import DayNightToggle from '../Theme/DayNightToggle'

const navItems = [
  {
    label: 'About Me',
    href: '/#aboutme',
  },
  {
    label: 'Skills',
    href: '/#skills',
  },
  {
    label: 'Certifications',
    href: '/#certifications',
  },
]

const Navbar = () => {
  const [isVisible, setIsVisible] = useState(false)
  const pathname = usePathname()

  const toggleMenu = () => {
    setIsVisible(!isVisible)
  }

  return (
    <nav className="movement-inner bg-primary relative">
      <div className="mx-auto flex h-15 w-dvw items-center px-4 py-1">
        {/* Logo on the left */}
        <div className="animate-fade-up text-primary-content relative flex items-center transition-all duration-300 md:static">
          <Image src={logo2} alt="Logo" width={40}/>
        </div>

        {/* Navigation items centered */}
        <ul
          className={`${
            isVisible ? 'flex' : 'hidden'
          } animate-fade-in absolute top-16 left-0 z-10 h-dvh w-dvw flex-col md:static md:top-0 md:flex md:h-full md:w-auto md:flex-row md:mx-auto md:gap-8 lg:gap-12`}>
          {navItems.map(({ label, href }) => (
            <li
              key={href}
              onClick={() => setIsVisible(false)}
              className="flex items-center px-4 text-2xl md:text-base lg:px-4">
              <Link
                href={href}
                className={`text-primary-content hover:text-neutral w-full py-7 transition-all duration-150 md:py-0 ${
                  pathname === href ? 'text-neutral cursor-text' : ''
                }`}>
                {label}
              </Link>
            </li>
          ))}
        </ul>
        
        {/* Mobile menu button */}
        <div className="md:hidden ml-auto mr-8">
          <button onClick={toggleMenu}>
            {isVisible ? (
              <CloseIcon className="text-primary-content" />
            ) : (
              <BurgerIcon className="text-primary-content" />
            )}
          </button>
        </div>
        
        {/* Theme Toggle Switch - positioned at far right */}
        <div className="absolute right-0 scale-50 flex items-center">
          <DayNightToggle />
        </div>
      </div>
    </nav>
  )
}

export default Navbar



