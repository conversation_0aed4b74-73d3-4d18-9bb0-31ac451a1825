'use client'
import { ReactNode, useEffect, useRef, useState } from 'react'

type MarqueeWrapperProps = {
  children: ReactNode
  className?: string
}

type MarqueeAnimationType = (
  element: HTMLElement,
  elementWidth: number,
  windowWidth: number,
) => void

const marqueeAnimation: MarqueeAnimationType = (element, elementWidth, windowWidth) => {
  element.animate(
    [{ transform: 'translateX(0)' }, { transform: `translateX(${windowWidth - elementWidth}px)` }],
    {
      duration: 20000,
      easing: 'linear',
      direction: 'alternate',
      iterations: Infinity,
    },
  )
}

const MarqueeWrapper: React.FC<MarqueeWrapperProps> = ({ children, className = '' }) => {
  const elementRef = useRef<HTMLDivElement>(null)
  const [windowWidth, setWindowWidth] = useState(0)

  useEffect(() => {
    setWindowWidth(window.innerWidth)

    if (elementRef.current) {
      const elementWidth = elementRef.current.getBoundingClientRect().width
      marqueeAnimation(elementRef.current as HTMLElement, elementWidth, windowWidth)
    }

    const handleResize = () => setWindowWidth(window.innerWidth)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [windowWidth])

  return (
    <div className={`relative bg-primary overflow-x-hidden ${className}`}>
      {/* iOS-style Liquid Glass Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-violet-500/5 to-purple-600/10"></div>

      {/* Floating glass orbs with iOS-style blur */}
      <div className="absolute top-10 left-1/4 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-10 right-1/4 w-96 h-96 bg-gradient-to-br from-violet-400/15 to-purple-600/25 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-10 w-64 h-64 bg-gradient-to-br from-blue-300/10 to-violet-400/15 rounded-full blur-2xl animate-pulse delay-500"></div>

      {/* Additional ambient lighting */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-blue-500/2 to-violet-500/3"></div>

      <div className="inter w-max whitespace-nowrap p-5 lg:p-7 relative z-10" ref={elementRef}>
        {children}
      </div>
    </div>
  )
}

export default MarqueeWrapper
