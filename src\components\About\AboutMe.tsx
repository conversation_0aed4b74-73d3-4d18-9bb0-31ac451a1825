'use client'

import React, { useState } from 'react';
import { Briefcase, GraduationCap, Heart, Building2, Palette, Code, Server } from 'lucide-react';
import chileFlag from '@/assets/images/main/chile.png';
import ibmLogo from '@/assets/images/main/ibm.png';
import itauLogo from '@/assets/images/main/itau.png';
import konexaLogo from '@/assets/images/main/konexa.png';
import exynkoLogo from '@/assets/images/main/exynko.jpg';
import duocucLogo from '@/assets/images/main/duoc.png';
import Image from 'next/image';

export default function AboutMe() {
  const [activeTab, setActiveTab] = useState('overview');
  const currentYear = new Date().getFullYear();
  const initialAge = 1994;
  const initialProfessionalExperienceYear = 2017;
  const experienceYears = currentYear-initialProfessionalExperienceYear; 
  const currentAge = currentYear-initialAge;

  const experience = [
    {
      title: 'Technical Leader',
      company: 'IBM de Chile S.A.C',
      period: 'Abril 2025 - <span class="text-green-400 font-bold animate-pulse">Presente</span>',
      description: 'Lidero células de trabajo con metodología Scrum, diseñando soluciones cloud en AWS con Nest.js para microservicios y Angular para frontend. Tomo decisiones técnicas sobre productos de valor y nuevas plataformas, diagramando arquitecturas cloud con Kubernetes y GitLab para asegurar calidad y claridad en cada sprint.',
      icon: 'ibm'
    },
    {
      title: 'Tech Lead',
      company: 'Itaú Chile',
      period: 'Agosto 2022 - Abril 2025',
      description: 'Lideré un equipo de 15 desarrolladores diseñando soluciones cloud en AWS con arquitectura de Microservicios usando Nest.js y .NET Core 8, Serverless e IA. Implementé principios SOLID, DDD y metodologías DevOps con CI/CD. Pionero en soluciones con LLM (Bedrock) y automatización de desarrollos, mejorando la velocidad de entregas y calidad del código en el sector bancario.',
      icon: 'itau'
    },
    {
      title: 'Backend Developer',
      company: 'Konexa',
      period: 'Abril 2022 - Agosto 2022',
      description: 'Desarrollé Microservicios con .NET Core 8+ y Node.js (Nest.js) aplicando DDD y principios SOLID. Implementé Lambda functions, Glue ETL con PySpark, y configuré pipelines CI/CD en AWS EKS. Integré servicios con API Connect de IBM y pasarelas de pago como Webpay.',
      icon: 'konexa',
      note: 'Internalización a Itaú Chile'
    },
    {
      title: 'Senior Software Engineer',
      company: 'Exynko',
      period: 'Enero 2019 - Agosto 2021',
      description: 'Desarrollé soluciones empresariales con C# MVC y Angular, integrando sistemas bancarios y Webpay. Gestioné incidentes en Medios de Pago y Finanzas, diseñé arquitecturas con Enterprise Architect y desarrollé portales internos de RRHH con SQL Server y autenticación JWT.',
      icon: 'exynko'
    }
  ];

  const education = [
    {
      degree: 'Ingeniería en Informática',
      institution: 'Duoc UC',
      year: 'Enero 2014 - Diciembre 2017',
      description: 'Formación en diseño, desarrollo e implementación de soluciones informáticas complejas. Enfoque en gestión de proyectos interdisciplinarios, aplicación de estándares y metodologías, con pensamiento crítico y ética profesional para resolver necesidades organizacionales.',
      icon: 'duoc',
      completed: true
    },
    {
      degree: 'Analista Programador',
      institution: 'Duoc UC',
      year: 'Enero 2014 - Junio 2016',
      description: 'Formación en desarrollo de soluciones de software con estándares y metodologías establecidas. Enfoque en trabajo colaborativo, pensamiento crítico y comprensión de necesidades organizacionales para proyectos interdisciplinarios.',
      icon: 'duocuc',
      completed: true
    }
  ];



  return (
    <section id="aboutme" className="relative bg-primary py-12 sm:py-16 lg:py-20 overflow-hidden rounded-[2rem] sm:rounded-[3rem] lg:rounded-[4rem] mx-2 sm:mx-4 my-4 sm:my-6 lg:my-8">
      {/* iOS-style Liquid Glass Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-violet-500/5 to-purple-600/10"></div>

      {/* Floating glass orbs with iOS-style blur */}
      <div className="absolute top-10 left-1/4 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-10 right-1/4 w-96 h-96 bg-gradient-to-br from-violet-400/15 to-purple-600/25 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-10 w-64 h-64 bg-gradient-to-br from-blue-300/10 to-violet-400/15 rounded-full blur-2xl animate-pulse delay-500"></div>

      {/* Additional ambient lighting */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-blue-500/2 to-violet-500/3"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral mb-4 sm:mb-6 drop-shadow-lg">
            About Me
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-tertiary-content max-w-3xl mx-auto leading-relaxed drop-shadow-sm px-4 sm:px-0">
            Aportando conocimiento con dirección a la innovación y transformación digital
          </p>
        </div>

        <div className="bg-white/5 backdrop-blur-3xl rounded-[1.5rem] sm:rounded-[2rem] lg:rounded-[3rem] p-4 sm:p-6 lg:p-8 border border-white/10 shadow-2xl relative overflow-hidden">
          {/* iOS-style container surface highlights */}
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/10 to-transparent"></div>

          {/* Inner gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 via-transparent to-violet-600/5 rounded-[3rem]"></div>

          <div className="grid lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 relative z-10">
            {/* Sidebar con foto y contacto */}


            {/* Contenido principal */}
            <div className="lg:col-span-3">
            {/* Tab Navigation */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mb-6 sm:mb-8 p-2 sm:p-3 relative">
              {/* Removed glass effect container */}

              {[
                { id: 'overview', label: 'Summary', icon: Heart },
                { id: 'experience', label: 'Work Experience', icon: Briefcase },
                { id: 'education', label: 'Education', icon: GraduationCap }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`relative flex-1 py-3 sm:py-4 px-3 sm:px-6 rounded-xl sm:rounded-2xl transition-all duration-700 flex items-center justify-center space-x-1 sm:space-x-2 backdrop-blur-2xl ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-br from-blue-500/30 to-violet-600/30 text-neutral shadow-2xl border border-white/20 transform scale-[1.02] before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/10 before:to-transparent before:rounded-2xl'
                      : 'text-tertiary-content hover:text-neutral hover:bg-white/10 hover:backdrop-blur-3xl hover:shadow-xl hover:border hover:border-white/10 hover:scale-[1.01]'
                  }`}
                >
                  <tab.icon className="w-4 h-4 sm:w-5 sm:h-5 relative z-10" />
                  <span className="font-medium relative z-10 text-sm sm:text-base">{tab.label}</span>

                  {/* iOS-style active indicator */}
                  {activeTab === tab.id && (
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-violet-500/20 rounded-2xl animate-pulse"></div>
                  )}
                </button>
              ))}
            </div>

            {/* iOS-style Liquid Glass Content Container */}
            <div className="bg-white/5 backdrop-blur-3xl rounded-[1rem] sm:rounded-[1.5rem] lg:rounded-[2rem] p-4 sm:p-6 lg:p-10 border border-white/10 shadow-2xl min-h-[400px] sm:min-h-[500px] relative overflow-hidden">
              {/* iOS-style multi-layer glass effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-violet-600/8 rounded-[2rem]"></div>
              <div className="absolute inset-0 bg-gradient-to-tl from-white/5 via-transparent to-blue-400/5 rounded-[2rem]"></div>

              {/* iOS-style surface highlights */}
              <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              <div className="absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/10 to-transparent"></div>

              {/* Floating particles for depth */}
              <div className="absolute top-8 right-8 w-2 h-2 bg-blue-400/30 rounded-full animate-pulse"></div>
              <div className="absolute bottom-12 left-12 w-1 h-1 bg-violet-400/40 rounded-full animate-pulse delay-300"></div>

              <div className="relative z-10">
                {activeTab === 'overview' && (
                  <div className="space-y-8">
                    <h3 className="text-2xl sm:text-3xl font-bold text-neutral mb-6 sm:mb-8 drop-shadow-lg">Sobre Mi</h3>
                    <div className="text-tertiary-content leading-relaxed">
                      <div className="text-sm sm:text-base lg:text-lg bg-white/5 backdrop-blur-2xl p-6 sm:p-8 lg:p-10 rounded-2xl sm:rounded-3xl border border-white/10 shadow-xl relative overflow-hidden hover:bg-white/8 transition-all duration-500">
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent"></div>
                        <p className="relative z-10">
                          Ingeniero en informática con {experienceYears} años
                          de experiencia en desarrollo de Soluciones informáticas, 
                          capacidad de trabajar en equipo, gran empatía, siempre en busca de mejoras y automatización, 
                          actualmente generando diseños de soluciones Cloud de AWS con foco en Microservicios, Serverless y GlueETL, 
                          Constantemente aprendiendo Arquitectura de Software y diseño de Soluciones.
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mt-8 sm:mt-12">
                      {/* Years of Experience */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-4 sm:p-6 lg:p-8 rounded-2xl sm:rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent rounded-2xl sm:rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/40 to-transparent"></div>
                        <div className="text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral drop-shadow-lg relative z-10">{experienceYears}</div>
                        <div className="text-tertiary-content mt-2 sm:mt-3 relative z-10 text-xs sm:text-sm lg:text-base">Años de Experiencia</div>
                      </div>

                      {/* Age */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-4 sm:p-6 lg:p-8 rounded-2xl sm:rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-violet-500/10 to-transparent rounded-2xl sm:rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-violet-400/40 to-transparent"></div>
                        <div className="text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral drop-shadow-lg relative z-10">{currentAge}</div>
                        <div className="text-tertiary-content mt-2 sm:mt-3 relative z-10 text-xs sm:text-sm lg:text-base">Años</div>
                      </div>

                      {/* Nationality */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-4 sm:p-6 lg:p-8 rounded-2xl sm:rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-transparent rounded-2xl sm:rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-emerald-400/40 to-transparent"></div>
                        <div className="relative z-10 flex justify-center">
                          <Image
                            src={chileFlag}
                            alt="Chile Flag"
                            width={40}
                            height={40}
                            className="drop-shadow-lg sm:w-[50px] sm:h-[50px] lg:w-[60px] lg:h-[60px]"
                          />
                        </div>
                        <div className="text-tertiary-content mt-2 sm:mt-3 relative z-10 text-xs sm:text-sm lg:text-base">Chile</div>
                      </div>

                      {/* Location */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-4 sm:p-6 lg:p-8 rounded-2xl sm:rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-transparent rounded-2xl sm:rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-orange-400/40 to-transparent"></div>
                        <div className="text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral drop-shadow-lg relative z-10">📍</div>
                        <div className="text-tertiary-content mt-2 sm:mt-3 relative z-10 text-xs sm:text-sm lg:text-base">Los Ángeles, Bio-Bio</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>


              {activeTab === 'experience' && (
                <div>
                  <h3 className="text-2xl sm:text-3xl font-bold text-neutral mb-6 sm:mb-8 drop-shadow-lg">Experiencia Profesional</h3>
                  <div className="space-y-6 sm:space-y-8">
                    {experience.map((exp, index) => (
                      <div key={index} className="bg-white/5 backdrop-blur-2xl p-6 sm:p-8 lg:p-10 rounded-2xl sm:rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-[1.02] transition-all duration-500 relative overflow-hidden group">
                        {/* iOS-style gradient accent */}
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-400 via-violet-500 to-blue-600 rounded-full"></div>

                        {/* Surface highlight */}
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent"></div>

                        {/* Hover gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-violet-600/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <div className="relative z-10">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6">
                            <h4 className="text-lg sm:text-xl lg:text-2xl font-semibold text-neutral drop-shadow-sm">{exp.title}</h4>
                            <div className="flex flex-col items-start sm:items-end mt-2 sm:mt-0">
                              <span
                                className="text-blue-400 font-medium bg-blue-500/10 backdrop-blur-xl px-4 sm:px-6 py-2 sm:py-3 rounded-xl sm:rounded-2xl border border-blue-400/20 shadow-lg text-sm sm:text-base"
                                dangerouslySetInnerHTML={{ __html: exp.period }}
                              ></span>
                              {exp.note && (
                                <span className="text-red-400 font-medium text-xs sm:text-sm mt-1 bg-red-500/10 backdrop-blur-xl px-3 py-1 rounded-lg border border-red-400/20">
                                  ({exp.note})
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 sm:space-x-3 mb-3 sm:mb-4">
                            {exp.icon === 'ibm' ? (
                              <Image
                                src={ibmLogo}
                                alt="IBM Logo"
                                width={40}
                                height={40}
                                className="sm:w-10 sm:h-10"
                              />
                            ) : exp.icon === 'itau' ? (
                              <Image
                                src={itauLogo}
                                alt="Itaú Logo"
                                width={40}
                                height={40}
                                className="sm:w-10 sm:h-10"
                              />
                            ) : exp.icon === 'konexa' ? (
                              <Image
                                src={konexaLogo}
                                alt="Konexa Logo"
                                width={40}
                                height={40}
                                className="sm:w-10 sm:h-10"
                              />
                            ) : exp.icon === 'exynko' ? (
                              <Image
                                src={exynkoLogo}
                                alt="Exynko Logo"
                                width={40}
                                height={40}
                                className="sm:w-10 sm:h-10"
                              />
                            ) : (
                              React.createElement(exp.icon as any, { className: "w-5 h-5 sm:w-6 sm:h-6 text-blue-400" })
                            )}
                            <p className="text-violet-300 font-medium text-base sm:text-lg">{exp.company}</p>
                          </div>
                          <p className="text-tertiary-content leading-relaxed text-sm sm:text-base">{exp.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'education' && (
                <div>
                  <h3 className="text-2xl sm:text-3xl font-bold text-neutral mb-6 sm:mb-8 drop-shadow-lg">Educación</h3>
                  <div className="space-y-6 sm:space-y-8">
                    {education.map((edu, index) => (
                      <div key={index} className="bg-white/5 backdrop-blur-2xl p-6 sm:p-8 lg:p-10 rounded-2xl sm:rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-[1.02] transition-all duration-500 relative overflow-hidden group">
                        {/* iOS-style gradient accent */}
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-violet-400 via-purple-500 to-violet-600 rounded-full"></div>

                        {/* Surface highlight */}
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-violet-400/30 to-transparent"></div>

                        {/* Hover gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 to-purple-600/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <div className="relative z-10">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6">
                            <h4 className="text-lg sm:text-xl lg:text-2xl font-semibold text-neutral drop-shadow-sm">{edu.degree}</h4>
                            <div className="flex flex-col items-start sm:items-end mt-2 sm:mt-0">
                              <span className="text-violet-400 font-medium bg-violet-500/10 backdrop-blur-xl px-4 sm:px-6 py-2 sm:py-3 rounded-xl sm:rounded-2xl border border-violet-400/20 shadow-lg text-sm sm:text-base">
                                {edu.year}
                              </span>
                              {edu.completed && (
                                <span className="text-green-400 font-bold text-xs sm:text-sm mt-1 bg-green-500/10 backdrop-blur-xl px-3 py-1 rounded-lg border border-green-400/20 animate-pulse">
                                  ✓ Título Obtenido
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 sm:space-x-3 mb-3 sm:mb-4">
                            {edu.icon === 'duoc' ? (
                              <Image
                                src={duocucLogo}
                                alt="Duoc UC Logo"
                                width={40}
                                height={40}
                                className="sm:w-10 sm:h-10"
                              />
                            ) : edu.icon === 'duocuc' ? (
                              <Image
                                src={duocucLogo}
                                alt="Duoc UC Logo"
                                width={40}
                                height={40}
                                className="sm:w-10 sm:h-10"
                              />
                            ) : (
                              <GraduationCap className="w-5 h-5 sm:w-6 sm:h-6 text-violet-400" />
                            )}
                            <p className="text-blue-300 font-medium text-base sm:text-lg">{edu.institution}</p>
                          </div>
                          {edu.description && (
                            <p className="text-tertiary-content leading-relaxed text-sm sm:text-base">{edu.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>


                </div>
              )}
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}





