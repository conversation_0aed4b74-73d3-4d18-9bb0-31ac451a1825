'use client'

import { useState } from 'react';
import { Briefcase, GraduationCap, Heart, Building2, Palette } from 'lucide-react';
import Image from 'next/image';
import chileFlag from '@/assets/images/main/chile.png';

export default function AboutMe() {
  const [activeTab, setActiveTab] = useState('overview');
  const initialProfessionalExperienceYear = 2017;
  const initialAge = 1994;
  const currentYear = new Date().getFullYear();
  const experienceYears = currentYear-initialProfessionalExperienceYear; 
  const currentAge = currentYear-initialAge;

  const experience = [
    {
      title: 'Senior Full Stack Developer',
      company: 'Tech Company',
      period: '2022 - Presente',
      description: 'Desarrollo de aplicaciones web modernas con React y Node.js',
      icon: Building2
    },
    {
      title: 'Frontend Developer',
      company: 'Digital Agency',
      period: '2020 - 2022',
      description: 'Creación de interfaces de usuario responsivas y optimizadas',
      icon: Palette
    }
  ];

  const education = [
    {
      degree: 'Ingeniería en Sistemas',
      institution: 'Universidad XYZ',
      year: '2019',
      description: 'Especialización en desarrollo de software'
    }
  ];



  return (
    <section id="aboutme" className="relative bg-primary py-20 overflow-hidden rounded-[4rem] mx-4 my-8">
      {/* iOS-style Liquid Glass Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-violet-500/5 to-purple-600/10"></div>

      {/* Floating glass orbs with iOS-style blur */}
      <div className="absolute top-10 left-1/4 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-10 right-1/4 w-96 h-96 bg-gradient-to-br from-violet-400/15 to-purple-600/25 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-10 w-64 h-64 bg-gradient-to-br from-blue-300/10 to-violet-400/15 rounded-full blur-2xl animate-pulse delay-500"></div>

      {/* Additional ambient lighting */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-blue-500/2 to-violet-500/3"></div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-neutral mb-6 drop-shadow-lg">
            About Me
          </h2>
          <p className="text-xl text-tertiary-content max-w-3xl mx-auto leading-relaxed drop-shadow-sm">
            Desarrollador Full Stack apasionado por crear soluciones digitales innovadoras
            que transforman ideas en experiencias extraordinarias.
          </p>
        </div>

        <div className="bg-white/5 backdrop-blur-3xl rounded-[3rem] p-8 border border-white/10 shadow-2xl relative overflow-hidden">
          {/* iOS-style container surface highlights */}
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/10 to-transparent"></div>

          {/* Inner gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 via-transparent to-violet-600/5 rounded-[3rem]"></div>

          <div className="grid lg:grid-cols-3 gap-12 relative z-10">
            {/* Sidebar con foto y contacto */}


            {/* Contenido principal */}
            <div className="lg:col-span-3">
            {/* Tab Navigation */}
            <div className="flex space-x-2 mb-8 p-3 relative">
              {/* Removed glass effect container */}

              {[
                { id: 'overview', label: 'Summary', icon: Heart },
                { id: 'experience', label: 'Work Experience', icon: Briefcase },
                { id: 'education', label: 'Education', icon: GraduationCap }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`relative flex-1 py-4 px-6 rounded-2xl transition-all duration-700 flex items-center justify-center space-x-2 backdrop-blur-2xl ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-br from-blue-500/30 to-violet-600/30 text-neutral shadow-2xl border border-white/20 transform scale-[1.02] before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/10 before:to-transparent before:rounded-2xl'
                      : 'text-tertiary-content hover:text-neutral hover:bg-white/10 hover:backdrop-blur-3xl hover:shadow-xl hover:border hover:border-white/10 hover:scale-[1.01]'
                  }`}
                >
                  <tab.icon className="w-5 h-5 relative z-10" />
                  <span className="font-medium relative z-10">{tab.label}</span>

                  {/* iOS-style active indicator */}
                  {activeTab === tab.id && (
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-violet-500/20 rounded-2xl animate-pulse"></div>
                  )}
                </button>
              ))}
            </div>

            {/* iOS-style Liquid Glass Content Container */}
            <div className="bg-white/5 backdrop-blur-3xl rounded-[2rem] p-10 border border-white/10 shadow-2xl min-h-[500px] relative overflow-hidden">
              {/* iOS-style multi-layer glass effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-violet-600/8 rounded-[2rem]"></div>
              <div className="absolute inset-0 bg-gradient-to-tl from-white/5 via-transparent to-blue-400/5 rounded-[2rem]"></div>

              {/* iOS-style surface highlights */}
              <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              <div className="absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/10 to-transparent"></div>

              {/* Floating particles for depth */}
              <div className="absolute top-8 right-8 w-2 h-2 bg-blue-400/30 rounded-full animate-pulse"></div>
              <div className="absolute bottom-12 left-12 w-1 h-1 bg-violet-400/40 rounded-full animate-pulse delay-300"></div>

              <div className="relative z-10">
                {activeTab === 'overview' && (
                  <div className="space-y-8">
                    <h3 className="text-3xl font-bold text-neutral mb-8 drop-shadow-lg">Mi Historia</h3>
                    <div className="text-tertiary-content leading-relaxed">
                      <div className="text-lg bg-white/5 backdrop-blur-2xl p-10 rounded-3xl border border-white/10 shadow-xl relative overflow-hidden hover:bg-white/8 transition-all duration-500">
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent"></div>
                        <p className="relative z-10">
                          Hola! Soy un desarrollador Full Stack con más de 5 años de experiencia
                          creando aplicaciones web modernas y escalables. Mi pasión por la tecnología
                          comenzó durante mis estudios universitarios y desde entonces no he parado
                          de aprender y evolucionar. Me especializo en el ecosistema de JavaScript,
                          particularmente en React, Next.js y Node.js. Disfruto trabajando tanto en el
                          frontend como en el backend, lo que me permite tener una visión integral de
                          los proyectos. Cuando no estoy programando, me gusta contribuir a proyectos
                          open source, escribir artículos técnicos y explorar nuevas tecnologías.
                          También soy un entusiasta del café y los videojuegos.
                        </p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                      {/* Years of Experience */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-8 rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/40 to-transparent"></div>
                        <div className="text-5xl font-bold text-neutral drop-shadow-lg relative z-10">{experienceYears}</div>
                        <div className="text-tertiary-content mt-3 relative z-10">Años de Experiencia</div>
                      </div>

                      {/* Age */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-8 rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-violet-500/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-violet-400/40 to-transparent"></div>
                        <div className="text-5xl font-bold text-neutral drop-shadow-lg relative z-10">{currentAge}</div>
                        <div className="text-tertiary-content mt-3 relative z-10">Años</div>
                      </div>

                      {/* Nationality */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-8 rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-emerald-400/40 to-transparent"></div>
                        <div className="relative z-10 flex justify-center">
                          <Image
                            src={chileFlag}
                            alt="Chile Flag"
                            width={60}
                            height={60}
                            className="drop-shadow-lg"
                          />
                        </div>
                        <div className="text-tertiary-content mt-3 relative z-10">Chile</div>
                      </div>

                      {/* Location */}
                      <div className="text-center bg-white/5 backdrop-blur-2xl p-8 rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-105 transition-all duration-500 relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-orange-400/40 to-transparent"></div>
                        <div className="text-5xl font-bold text-neutral drop-shadow-lg relative z-10">📍</div>
                        <div className="text-tertiary-content mt-3 relative z-10">Los Ángeles, Region Bio-Bio</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>


              {activeTab === 'experience' && (
                <div>
                  <h3 className="text-3xl font-bold text-neutral mb-8 drop-shadow-lg">Experiencia Profesional</h3>
                  <div className="space-y-8">
                    {experience.map((exp, index) => (
                      <div key={index} className="bg-white/5 backdrop-blur-2xl p-10 rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-[1.02] transition-all duration-500 relative overflow-hidden group">
                        {/* iOS-style gradient accent */}
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-400 via-violet-500 to-blue-600 rounded-full"></div>

                        {/* Surface highlight */}
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent"></div>

                        {/* Hover gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-violet-600/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <div className="relative z-10">
                          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                            <h4 className="text-2xl font-semibold text-neutral drop-shadow-sm">{exp.title}</h4>
                            <span className="text-blue-400 font-medium bg-blue-500/10 backdrop-blur-xl px-6 py-3 rounded-2xl border border-blue-400/20 shadow-lg mt-2 md:mt-0">
                              {exp.period}
                            </span>
                          </div>
                          <div className="flex items-center space-x-3 mb-4">
                            <exp.icon className="w-6 h-6 text-blue-400" />
                            <p className="text-violet-300 font-medium text-lg">{exp.company}</p>
                          </div>
                          <p className="text-tertiary-content leading-relaxed">{exp.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'education' && (
                <div>
                  <h3 className="text-3xl font-bold text-neutral mb-8 drop-shadow-lg">Educación</h3>
                  <div className="space-y-8">
                    {education.map((edu, index) => (
                      <div key={index} className="bg-white/5 backdrop-blur-2xl p-10 rounded-3xl border border-white/10 shadow-xl hover:bg-white/8 hover:shadow-2xl hover:scale-[1.02] transition-all duration-500 relative overflow-hidden group">
                        {/* iOS-style gradient accent */}
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-violet-400 via-purple-500 to-violet-600 rounded-full"></div>

                        {/* Surface highlight */}
                        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-violet-400/30 to-transparent"></div>

                        {/* Hover gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 to-purple-600/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <div className="relative z-10">
                          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                            <h4 className="text-2xl font-semibold text-neutral drop-shadow-sm">{edu.degree}</h4>
                            <span className="text-violet-400 font-medium bg-violet-500/10 backdrop-blur-xl px-6 py-3 rounded-2xl border border-violet-400/20 shadow-lg mt-2 md:mt-0">
                              {edu.year}
                            </span>
                          </div>
                          <div className="flex items-center space-x-3 mb-4">
                            <GraduationCap className="w-6 h-6 text-violet-400" />
                            <p className="text-blue-300 font-medium text-lg">{edu.institution}</p>
                          </div>
                          <p className="text-tertiary-content leading-relaxed">{edu.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>


                </div>
              )}
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}





