import { FC, HTMLAttributes, RefObject } from 'react'

interface EllipseProps extends HTMLAttributes<SVGElement> {
  ref: RefObject<SVGSVGElement>
}

const Ellipse: FC<EllipseProps> = ({ ref, ...props }) => {
  return (
    <svg
      ref={ref}
      width="412"
      height="413"
      viewBox="0 0 412 413"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <defs>
        {/* Light Theme Gradient - Blue to Violet */}
        <linearGradient id="liquidGlassLight" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
          <stop offset="25%" stopColor="#6366F1" stopOpacity="0.9" />
          <stop offset="50%" stopColor="#8B5CF6" stopOpacity="1" />
          <stop offset="75%" stopColor="#A855F7" stopOpacity="0.9" />
          <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.7" />
        </linearGradient>

        {/* Dark Theme Gradient - Pure Blue */}
        <linearGradient id="liquidGlassDark" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.9" />
          <stop offset="25%" stopColor="#60A5FA" stopOpacity="1" />
          <stop offset="50%" stopColor="#2563EB" stopOpacity="1" />
          <stop offset="75%" stopColor="#1D4ED8" stopOpacity="1" />
          <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.9" />
        </linearGradient>

        {/* Glow Effect */}
        <filter id="glow">
          <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      {/* Main Circle with Theme-Adaptive Liquid Glass Effect */}
      <circle
        cx="206"
        cy="206.401"
        r="204.5"
        strokeWidth="3"
        strokeLinecap="round"
        strokeDasharray="18 36 54 72"
        filter="url(#glow)"
        className="stroke-[url(#liquidGlassLight)] dark:stroke-[url(#liquidGlassDark)] transition-all duration-500"
      />


    </svg>
  )
}

export default Ellipse
