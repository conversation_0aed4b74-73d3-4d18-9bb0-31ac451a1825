import { FC, HTMLAttributes, RefObject } from 'react'

interface EllipseProps extends HTMLAttributes<SVGElement> {
  ref: RefObject<SVGSVGElement>
}

const Ellipse: FC<EllipseProps> = ({ ref, ...props }) => {
  return (
    <svg
      ref={ref}
      width="412"
      height="413"
      viewBox="0 0 412 413"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <defs>
        {/* Liquid Glass Gradient */}
        <linearGradient id="liquidGlass" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
          <stop offset="25%" stopColor="#6366F1" stopOpacity="0.9" />
          <stop offset="50%" stopColor="#8B5CF6" stopOpacity="1" />
          <stop offset="75%" stopColor="#A855F7" stopOpacity="0.9" />
          <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.7" />
        </linearGradient>

        {/* Glow Effect */}
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>


      </defs>

      {/* Main Circle with Liquid Glass Effect */}
      <circle
        cx="206"
        cy="206.401"
        r="204.5"
        stroke="url(#liquidGlass)"
        strokeWidth="4"
        strokeLinecap="round"
        strokeDasharray="18 36 54 72"
        filter="url(#glow)"
        opacity="0.9"
      />


    </svg>
  )
}

export default Ellipse
