'use client'

import { useEffect, useState } from 'react'
import '@/assets/css/new-theme-toggle.css'

const ThemeToggle = () => {
  const [theme, setTheme] = useState('dark')

  useEffect(() => {
    // Initialize theme from localStorage or default to dark
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') || 'dark'
      setTheme(savedTheme)
      document.documentElement.setAttribute('data-theme', savedTheme)
    }
  }, [])

  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark'
    setTheme(newTheme)
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', newTheme)
      document.documentElement.setAttribute('data-theme', newTheme)
    }
  }

  return (
    <div className="toggle-container">
      <div className="toggle-wrap">
        <input 
          type="checkbox" 
          className="toggle-input" 
          id="toggle" 
          checked={theme === 'light'}
          onChange={toggleTheme}
        />
        <label htmlFor="toggle" className="toggle-track">
          <div className="track-line"></div>
          <div className="toggle-thumb">
            <div className="thumb-core"></div>
            <div className="thumb-inner"></div>
            <div className="thumb-scan"></div>
            <div className="thumb-particle"></div>
            <div className="thumb-particle"></div>
            <div className="thumb-particle"></div>
          </div>
          <div className="data-text off">OFF</div>
          <div className="data-text on">ON</div>
          <div className="status-indicator off"></div>
          <div className="status-indicator on"></div>
          <div className="energy-rings">
            <div className="energy-ring"></div>
            <div className="energy-ring"></div>
            <div className="energy-ring"></div>
          </div>
          <div className="interface-line"></div>
          <div className="interface-line"></div>
          <div className="interface-line"></div>
          <div className="interface-line"></div>
          <div className="holo-glow"></div>
        </label>
      </div>
      <div className="toggle-label">System Power</div>
    </div>
  )
}

export default ThemeToggle
