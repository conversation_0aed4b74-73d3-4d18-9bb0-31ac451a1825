image: node

build:
  stage: build
  cache:
    paths:
      - node_modules/
  before_script:
    - corepack enable
    - corepack prepare pnpm@latest-9 --activate
    - pnpm config set store-dir .pnpm-store
  script:
    - pnpm i
    - pnpm run build
  artifacts:
    paths:
      - /builds/portfolio/portfolio-web
  only:
    - development
    - main

pages:
  stage: deploy
  needs: [build]
  before_script:
    - find public -mindepth 1 -maxdepth 1 -type d | xargs rm -rf
    - find public -type f -name "*.html" | xargs rm -rf
  script:
    # Build application and move content to public folder
    - mv out/* public
  after_script:
    # Cleanup
    - rm -rf out
  artifacts:
    paths:
      - public
  only:
    - main


